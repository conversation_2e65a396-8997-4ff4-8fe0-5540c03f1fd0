package com.lfb.android.footprint.Manager

import android.content.Context
import kotlinx.coroutines.runBlocking

/**
 * 磁盘缓存序列化测试
 * 用于验证修复后的序列化功能是否正常工作
 */
object DiskCacheSerializationTest {

    /**
     * 测试所有数据类型的序列化和反序列化
     */
    fun testAllDataTypes(context: Context) {
        runBlocking {
            println("=== 开始测试磁盘缓存序列化功能 ===")
            
            val diskCacheManager = DiskCacheManager.getInstance(context)
            
            try {
                // 测试1: Triple<Int, Int, Int> 类型 (如日期数据)
                testTripleData(diskCacheManager)
                
                // 测试2: Pair<Long, Double> 类型 (如统计数据)
                testPairData(diskCacheManager)
                
                // 测试3: List<Int> 类型 (如年份列表)
                testIntListData(diskCacheManager)
                
                // 测试4: String 类型
                testStringData(diskCacheManager)
                
                println("✅ 所有数据类型序列化测试通过！")
                
            } catch (e: Exception) {
                println("❌ 序列化测试失败: ${e.message}")
                e.printStackTrace()
            }
            
            println("=== 磁盘缓存序列化功能测试完成 ===")
        }
    }

    private suspend fun testTripleData(diskCacheManager: DiskCacheManager) {
        println("测试 Triple<Int, Int, Int> 数据类型...")
        
        val testKey = "test_triple"
        val testData = Triple(2025, 6, 26)
        val ttl = 60000L
        
        // 存储
        diskCacheManager.putTriple(testKey, testData, ttl)
        println("  存储数据: $testData")
        
        // 读取
        val retrievedData = diskCacheManager.getTriple(testKey)
        println("  读取数据: $retrievedData")
        
        // 验证
        if (retrievedData == testData) {
            println("  ✅ Triple 数据测试通过")
        } else {
            println("  ❌ Triple 数据测试失败: 期望 $testData, 实际 $retrievedData")
        }
        
        // 清理
        diskCacheManager.remove(testKey)
    }

    private suspend fun testPairData(diskCacheManager: DiskCacheManager) {
        println("测试 Pair<Long, Double> 数据类型...")
        
        val testKey = "test_pair"
        val testData = Pair(12345L, 67.89)
        val ttl = 60000L
        
        // 存储
        diskCacheManager.putStatsPair(testKey, testData, ttl)
        println("  存储数据: $testData")
        
        // 读取
        val retrievedData = diskCacheManager.getStatsPair(testKey)
        println("  读取数据: $retrievedData")
        
        // 验证
        if (retrievedData == testData) {
            println("  ✅ Pair 数据测试通过")
        } else {
            println("  ❌ Pair 数据测试失败: 期望 $testData, 实际 $retrievedData")
        }
        
        // 清理
        diskCacheManager.remove(testKey)
    }

    private suspend fun testIntListData(diskCacheManager: DiskCacheManager) {
        println("测试 List<Int> 数据类型...")
        
        val testKey = "test_int_list"
        val testData = listOf(2020, 2021, 2022, 2023, 2024, 2025)
        val ttl = 60000L
        
        // 存储
        diskCacheManager.putIntList(testKey, testData, ttl)
        println("  存储数据: $testData")
        
        // 读取
        val retrievedData = diskCacheManager.getIntList(testKey)
        println("  读取数据: $retrievedData")
        
        // 验证
        if (retrievedData == testData) {
            println("  ✅ List<Int> 数据测试通过")
        } else {
            println("  ❌ List<Int> 数据测试失败: 期望 $testData, 实际 $retrievedData")
        }
        
        // 清理
        diskCacheManager.remove(testKey)
    }

    private suspend fun testStringData(diskCacheManager: DiskCacheManager) {
        println("测试 String 数据类型...")
        
        val testKey = "test_string"
        val testData = "Hello, Disk Cache!"
        val ttl = 60000L
        
        // 存储
        diskCacheManager.putString(testKey, testData, ttl)
        println("  存储数据: $testData")
        
        // 读取
        val retrievedData = diskCacheManager.getString(testKey)
        println("  读取数据: $retrievedData")
        
        // 验证
        if (retrievedData == testData) {
            println("  ✅ String 数据测试通过")
        } else {
            println("  ❌ String 数据测试失败: 期望 $testData, 实际 $retrievedData")
        }
        
        // 清理
        diskCacheManager.remove(testKey)
    }

    /**
     * 测试通过 DataCacheManager 的集成功能
     */
    fun testDataCacheManagerIntegration(context: Context) {
        runBlocking {
            println("=== 测试 DataCacheManager 集成功能 ===")
            
            try {
                val dataCacheManager = DataCacheManager.getInstance(context)
                
                // 测试存储和读取 Triple 数据
                val testKey = "test_integration_triple"
                val testData = Triple(2025, 6, 26)
                val ttl = 60000L
                
                // 存储
                dataCacheManager.put(testKey, testData, ttl)
                println("通过 DataCacheManager 存储数据: $testData")
                
                // 清除内存缓存，强制从磁盘读取
                dataCacheManager.clearMemoryCache()
                
                // 读取
                val retrievedData = dataCacheManager.get<Triple<Int, Int, Int>>(testKey)
                println("从磁盘缓存读取数据: $retrievedData")
                
                // 验证
                if (retrievedData == testData) {
                    println("✅ DataCacheManager 集成测试通过")
                } else {
                    println("❌ DataCacheManager 集成测试失败")
                }
                
                // 清理
                dataCacheManager.remove(testKey)
                
            } catch (e: Exception) {
                println("❌ DataCacheManager 集成测试失败: ${e.message}")
                e.printStackTrace()
            }
            
            println("=== DataCacheManager 集成功能测试完成 ===")
        }
    }
}

/**
 * 在 MyApplication 中的使用示例:
 * 
 * override fun onCreate() {
 *     super.onCreate()
 *     // ... 其他初始化代码
 *     
 *     // 测试磁盘缓存序列化功能（仅在调试模式下）
 *     if (BuildConfig.DEBUG) {
 *         DiskCacheSerializationTest.testAllDataTypes(this)
 *         DiskCacheSerializationTest.testDataCacheManagerIntegration(this)
 *     }
 * }
 */
