package com.lfb.android.footprint

import android.Manifest
import android.app.AlertDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.os.Build
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import com.lfb.android.footprint.Manager.AppNotificationManager
import com.lfb.android.footprint.service.LocationService
import com.mapbox.geojson.Point
import com.mapbox.maps.CameraOptions
import com.mapbox.maps.EdgeInsets
import com.mapbox.maps.MapView
import com.mapbox.maps.extension.compose.animation.viewport.MapViewportState
import com.mapbox.maps.plugin.animation.MapAnimationOptions
import com.mapbox.maps.plugin.animation.easeTo

// 定义轨迹动画类型
enum class DrawPointAnimationType {
    FREE_MODE,
    FOLLOW_MODE,
    SCALE_MODE
}

open class BaseMapActivity : BaseActivity() {

    private val permissions = arrayOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION,
        Manifest.permission.ACCESS_BACKGROUND_LOCATION,
        Manifest.permission.FOREGROUND_SERVICE_LOCATION
    )

    private val locationPermissionRequest = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        when {
            permissions[Manifest.permission.ACCESS_FINE_LOCATION] == true ||
                    permissions[Manifest.permission.ACCESS_COARSE_LOCATION] == true -> {
                    checkAndRequestPermissions()
            }
        }
    }

    private val backgroundLocationPermissionRequest = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            startLocationService()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        checkAndRequestPermissions()
        AppNotificationManager.requestNotificationPermissionIfNeeded(this)

        setupMapUI()
    }

    // 供子类实现具体地图UI
    open fun setupMapUI() {

    }

    fun calculateTotalDistance(points: List<Point>): Double {
        if (points.size < 2) return 0.0
        var totalDistance = 0.0
        for (i in 0 until points.size - 1) {
            val start = Location("").apply {
                latitude = points[i].latitude()
                longitude = points[i].longitude()
            }
            val end = Location("").apply {
                latitude = points[i + 1].latitude()
                longitude = points[i + 1].longitude()
            }
            totalDistance += start.distanceTo(end)
        }
        return totalDistance
    }

    fun checkAndRequestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            when {
                checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED ||
                        checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED ||
                        (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE &&
                                checkSelfPermission(Manifest.permission.FOREGROUND_SERVICE_LOCATION) != PackageManager.PERMISSION_GRANTED) -> {
                    locationPermissionRequest.launch(
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                            arrayOf(
                                Manifest.permission.ACCESS_FINE_LOCATION,
                                Manifest.permission.ACCESS_COARSE_LOCATION,
                                Manifest.permission.FOREGROUND_SERVICE_LOCATION
                            )
                        } else {
                            arrayOf(
                                Manifest.permission.ACCESS_FINE_LOCATION,
                                Manifest.permission.ACCESS_COARSE_LOCATION
                            )
                        }
                    )
                }
                checkSelfPermission(Manifest.permission.ACCESS_BACKGROUND_LOCATION) != PackageManager.PERMISSION_GRANTED -> {
                    showBackgroundLocationRationale()
                }
                else -> {
                    startLocationService()
                }
            }
        } else {
            startLocationService()
        }
    }

    private fun showBackgroundLocationRationale() {
        AlertDialog.Builder(this)
            .setTitle("需要后台定位权限")
            .setMessage("为了持续记录您的运动轨迹，我们需要在应用处于后台时访问位置信息。请在接下来的系统对话框中选择\"始终允许\"。")
            .setPositiveButton("确定") { _, _ ->
                backgroundLocationPermissionRequest.launch(
                    Manifest.permission.ACCESS_BACKGROUND_LOCATION
                )
            }
            .setNegativeButton("取消", null)
            .show()
    }

    fun showRecordModeDialog(onModeSelected: (Int) -> Unit) {
        val modes = arrayOf("普通模式", "耗电模式", "省电模式")
        AlertDialog.Builder(this)
            .setTitle("切换记录模式")
            .setItems(modes) { _, which ->
                onModeSelected(which)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun startLocationService() {
        val serviceIntent = Intent(this, LocationService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(serviceIntent)
        } else {
            startService(serviceIntent)
        }
    }

    private fun stopLocationService() {
        val serviceIntent = Intent(this, LocationService::class.java)
        stopService(serviceIntent)
    }

    fun fitTrackAndAnimate(
        mapView: MapView,
        mapViewportState: MapViewportState,
        trackPoints: List<Point>
    ) {

        // 计算轨迹的边界框并调整地图视角
        if (trackPoints.size > 2) {
            mapView.getMapboxMap().cameraForCoordinates(
                trackPoints,
                CameraOptions.Builder().build(),
                EdgeInsets(100.0, 22.0, 100.0, 22.0), // 设置边距
                null, // maxZoom 可选
                null  // offset 可选
            ) { cameraOptions ->
                mapView.getMapboxMap().easeTo(
                    cameraOptions,
                    MapAnimationOptions.Builder()
                        .duration(300) // 动画时长，单位毫秒
                        .build()
                )
            }
        } else if (trackPoints.isNotEmpty()) {
            // 对于单点或两点，使用固定缩放级别和边距计算
            val newPoint = trackPoints.first()

            // 使用 Mapbox 的 easeTo 方法，配合 padding 参数
            mapView.getMapboxMap().easeTo(
                CameraOptions.Builder()
                    .center(newPoint)
                    .zoom(15.0)
                    .padding(EdgeInsets(400.0, 100.0, 400.0, 200.0))
                    .build(),
                MapAnimationOptions.Builder()
                    .duration(300)
                    .build()
            )
        }
    }

    fun fitTrackAndAnimateWithPadding(
        mapView: MapView,
        mapViewportState: MapViewportState,
        trackPoints: List<Point>,
        density: Float,
        topPaddingDp: Double = 100.0,
        leftPaddingDp: Double = 22.0,
        bottomPaddingDp: Double = 100.0,
        rightPaddingDp: Double = 22.0
    ) {

//        EdgeInsets(100.0, 22.0, 100.0, 22.0), // 设置边距

        // 将 dp 值转换为像素值
        val topPadding = topPaddingDp * density
        val leftPadding = leftPaddingDp * density
        var bottomPadding = bottomPaddingDp * density
        val rightPadding = rightPaddingDp * density

        // 计算轨迹的边界框并调整地图视角，支持自定义padding
        if (trackPoints.size > 2) {

            mapView.getMapboxMap().cameraForCoordinates(
                trackPoints,
                CameraOptions.Builder().build(),
                EdgeInsets(topPadding, leftPadding, bottomPadding, rightPadding), // 自定义边距
                null, // maxZoom 可选
                null  // offset 可选
            ) { cameraOptions ->
                mapView.getMapboxMap().easeTo(
                    cameraOptions,
                    MapAnimationOptions.Builder()
                        .duration(300) // 动画时长，单位毫秒
                        .build()
                )
            }
        } else if (trackPoints.isNotEmpty()) {
            // 对于单点或两点，使用固定缩放级别和自定义边距
            val newPoint = trackPoints.first()

            bottomPadding = (bottomPaddingDp * 1.5) * density

            // 使用 Mapbox 的 easeTo 方法，配合 padding 参数
            mapView.getMapboxMap().easeTo(
                CameraOptions.Builder()
                    .center(newPoint)
                    .zoom(15.0)
                    .padding(EdgeInsets(topPadding, leftPadding, bottomPadding, rightPadding))
                    .build(),
                MapAnimationOptions.Builder()
                    .duration(300)
                    .build()
            )
        }
    }

    companion object {
        private const val LOCATION_PERMISSION_REQUEST_CODE = 1001
        private const val BACKGROUND_LOCATION_PERMISSION_REQUEST_CODE = 1002
    }
} 