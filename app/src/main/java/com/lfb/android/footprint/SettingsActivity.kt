package com.lfb.android.footprint

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import com.lfb.android.footprint.ui.components.settingScreen.SettingsScreen
import com.lfb.android.footprint.ui.theme.ThemeUtils

class SettingsActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        // 根据用户的地图主题配置设置Activity主题
        setTheme(ThemeUtils.getActivityThemeForMapDisplayType(this))

        super.onCreate(savedInstanceState)

        setContent {
            SettingsScreen(
                onBackClick = { finish() },
                onImportExportClick = {
                    val intent = Intent(this@SettingsActivity, ImportExportActivity::class.java)
                    startActivity(intent)
                }
            )
        }
    }
}
