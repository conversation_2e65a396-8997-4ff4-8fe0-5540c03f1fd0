package com.lfb.android.footprint.ui.components.guideScreen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.launch
import com.lfb.android.footprint.R
import com.lfb.android.footprint.ui.theme.mainRedColor

@Composable
fun GuideScreen(
    onFinishGuide: () -> Unit
) {
    val pagerState = rememberPagerState(pageCount = { 2 })
    val coroutineScope = rememberCoroutineScope()
    
    // 获取屏幕尺寸
    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp
    val screenWidth = configuration.screenWidthDp.dp
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF161E32)) // 深蓝色背景，匹配设计稿
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            when (page) {
                0 -> FirstGuidePage(
                    screenWidth = screenWidth,
                    screenHeight = screenHeight,
                    onNextClick = {
                        coroutineScope.launch {
                            pagerState.animateScrollToPage(1)
                        }
                    }
                )
                1 -> SecondGuidePage(
                    screenWidth = screenWidth,
                    screenHeight = screenHeight,
                    onPreviousClick = {
                        coroutineScope.launch {
                            pagerState.animateScrollToPage(0)
                        }
                    },
                    onSkipClick = onFinishGuide
                )
            }
        }
        
        // 页面指示器
        Row(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 100.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            repeat(2) { index ->
                Box(
                    modifier = Modifier
                        .size(width = if (index == pagerState.currentPage) 24.dp else 8.dp, height = 8.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(
                            if (index == pagerState.currentPage) Color.White 
                            else Color.White.copy(alpha = 0.3f)
                        )
                )
            }
        }
    }
}

@Composable
private fun FirstGuidePage(
    screenWidth: androidx.compose.ui.unit.Dp,
    screenHeight: androidx.compose.ui.unit.Dp,
    onNextClick: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        // 背景图片
        Image(
            painter = painterResource(id = R.drawable.appguide_bg),
            contentDescription = null,
            contentScale = ContentScale.Crop
        )
        
        // 内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 36.dp),
            verticalArrangement = Arrangement.Bottom // 使子元素底部对齐

        ) {
            // 文本内容区域
            Column(
                modifier = Modifier.padding(bottom = 64.dp)
            ) {
                Text(
                    text = "你好，",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "我是足迹的开发者。",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "2017年的清明节（17.4.4），我和同事去了一次沙漠，徒步12小时，25.7公里，我们穿越了中国第七大沙漠库布其。",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                    lineHeight = 24.sp
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "这次旅行很累，也让我记忆深刻。",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "在回北京的路上有人在群里问，我们是从哪儿走到哪儿的？",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                    lineHeight = 24.sp
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "可遗憾的是，没有一个人能准确说出。",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "突然我就感觉挺悲哀的。",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "也许我的一生也会这样，不曾经历多少，遇过了多少，到最后，都会被淡忘。我们都是普通人，没有人记得我们的过去，就像我们这次的旅行一样，很快就会被忘记。",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                    lineHeight = 24.sp
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "所以，我写了这个可以记录一生轨迹的APP。",
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal
                )
            }

            // 底部区域
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 44.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Dev: lfb_CD",
                    color = Color.White.copy(alpha = 0.7f),
                    fontSize = 14.sp
                )
                
                Text(
                    text = "下一步",
                    color = mainRedColor,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.clickable { onNextClick() }
                )
            }
        }
    }
}

@Composable
private fun SecondGuidePage(
    screenWidth: androidx.compose.ui.unit.Dp,
    screenHeight: androidx.compose.ui.unit.Dp,
    onPreviousClick: () -> Unit,
    onSkipClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF161E32)) // 深蓝色背景
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 36.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Spacer(modifier = Modifier.height(80.dp))

            // 标题
            Text(
                text = "软件如何使用",
                color = Color.White,
                fontSize = 20.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // 内容区域
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "给予始终定位权限即可，APP将会在后台自动记录您的行程轨迹。",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Normal,
                    lineHeight = 24.sp
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "注意：不要划掉在后台运行的足迹，会导致无法记录轨迹。",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Normal,
                    lineHeight = 24.sp
                )

                Spacer(modifier = Modifier.height(40.dp))

                // 权限选项
                PermissionItem(
                    text = "始终允许定位",
                    isEnabled = true
                )

                Spacer(modifier = Modifier.height(16.dp))

                PermissionItem(
                    text = "导入相册图片中的定位信息",
                    isEnabled = true
                )

                Spacer(modifier = Modifier.height(32.dp))

                // 提示文本
                Row (
                    modifier = Modifier.fillMaxWidth()
                ){
                    val annotatedString = buildAnnotatedString {
                        append("只有")
                        withStyle(style = SpanStyle(color = mainRedColor, fontWeight = FontWeight.Medium)) {
                            append("始终允许")
                        }
                        append("定位，才能在切换至后台运行时仍然为您记录和绘制轨迹")
                    }

                    Text(
                        text = annotatedString,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Normal,
                        color = Color.White
                    )
                }

                Spacer(modifier = Modifier.height(40.dp))

                PermissionItem(
                    text = "打开iCloud自动备份功能",
                    isEnabled = true
                )
            }

            // 底部按钮区域
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 44.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "上一步",
                    color = Color.White.copy(alpha = 0.7f),
                    fontSize = 16.sp,
                    modifier = Modifier.clickable { onPreviousClick() }
                )

                Text(
                    text = "跳过",
                    color = mainRedColor,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.clickable { onSkipClick() }
                )
            }
        }
    }
}

@Composable
private fun PermissionItem(
    text: String,
    isEnabled: Boolean
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp)
            .background(
                color = mainRedColor,
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = text,
            color = Color.White,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )

        // 开关圆圈
        Box(
            modifier = Modifier
                .size(24.dp)
                .background(
                    color = Color.White,
                    shape = androidx.compose.foundation.shape.CircleShape
                )
        )
    }
}
