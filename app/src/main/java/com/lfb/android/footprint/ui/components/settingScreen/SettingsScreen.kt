package com.lfb.android.footprint.ui.components.settingScreen

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.lfb.android.footprint.R
import com.lfb.android.footprint.ui.theme.mainRedColor

@Composable
fun SettingsScreen(
    onBackClick: () -> Unit,
    onImportExportClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFF0F1419)) // 深蓝色背景，匹配设计稿
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {
            Spacer(modifier = Modifier.height(50.dp)) // 状态栏间距

            // 顶部会员信息卡片
            MemberInfoCard()

            Spacer(modifier = Modifier.height(20.dp))

            // 功能按钮区域
            FunctionButtonsSection(onImportExportClick = onImportExportClick)

            Spacer(modifier = Modifier.height(20.dp))

            // 功能卡片列表
            FunctionCardsList()

            Spacer(modifier = Modifier.height(20.dp))

            // 简单功能列表
            SimpleFunctionsList()

            Spacer(modifier = Modifier.height(120.dp)) // 底部按钮空间
        }

        // 底部返回按钮
        BackToMapButton(
            onClick = onBackClick,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 50.dp)
        )
    }
}


@Composable
private fun FunctionButtonsSection(onImportExportClick: () -> Unit = {}) {
    Row(
        modifier = Modifier.fillMaxWidth()
//            .background(
//                    Color(0xFF1E2A3A)
//                )
                ,
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        FunctionButton(
            iconRes = R.drawable.setting_input, // 临时使用现有图标
            text = "导入导出",
            onClick = onImportExportClick
        )
        Spacer(modifier = Modifier.height(6.dp))

        FunctionButton(
            iconRes = R.drawable.setting_icloud,
            text = "数据备份",
            onClick = { /* TODO */ }
        )

        Spacer(modifier = Modifier.height(6.dp))

        FunctionButton(
            iconRes = R.drawable.setting_locmodel,
            text = "定位模式",
            onClick = { /* TODO */ }
        )

        Spacer(modifier = Modifier.height(6.dp))

        FunctionButton(
            iconRes = R.drawable.setting_notice,
            text = "通知配置",
            onClick = { /* TODO */ }
        )
    }
}

@Composable
private fun FunctionButton(
    iconRes: Int,
    text: String,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable { onClick() }
            .padding(start = 0.dp, end = 0.dp, top = 2.dp, bottom = 8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(44.dp)
//                .background(
//                    Color(0xFF1E2A3A),
//                    RoundedCornerShape(8.dp),
//                )
            ,
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = iconRes),
                contentDescription = text,
                modifier = Modifier.size(20.dp),
                colorFilter = ColorFilter.tint(mainRedColor)
            )
        }

        Spacer(modifier = Modifier.height(6.dp))

        Text(
            text = text,
            color = Color.White,
            fontSize = 11.sp,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun FunctionCardsList() {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 足迹实验室
        FunctionCard(
            title = "足迹实验室",
            backgroundColor = Color(0xFF1E3A5F),
            onClick = { /* TODO */ }
        )

        // 常去地点
        FunctionCard(
            title = "常去地点",
            subtitle = "添加常去地点可以减少耗电量！",
            backgroundColor = Color(0xFF5A2A2A),
            onClick = { /* TODO */ }
        )
    }
}

@Composable
private fun SimpleFunctionsList() {
    Column(
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        val functionItems = listOf(
            "使用说明",
            "微信公众号",
            "联系作者",
            "分享「足迹」",
            "关于「足迹」",
            "鼓励「足迹」"
        )

        functionItems.forEach { item ->
            SimpleFunctionItem(
                title = item,
                onClick = { /* TODO */ }
            )
        }
    }
}

@Composable
private fun FunctionCard(
    title: String,
    subtitle: String? = null,
    backgroundColor: Color,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(if (subtitle != null) 70.dp else 50.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.CenterStart
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = title,
                    color = Color.White,
                    fontSize = 15.sp,
                    fontWeight = FontWeight.Medium
                )

                subtitle?.let {
                    Spacer(modifier = Modifier.height(3.dp))
                    Text(
                        text = it,
                        color = Color.White.copy(alpha = 0.6f),
                        fontSize = 11.sp
                    )
                }
            }
        }
    }
}

@Composable
private fun SimpleFunctionItem(
    title: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            color = Color.White,
            fontSize = 15.sp,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun BackToMapButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier
            .width(160.dp)
            .height(44.dp),
        shape = RoundedCornerShape(22.dp),
        border = BorderStroke(1.dp, mainRedColor),
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = Color.Transparent,
            contentColor = mainRedColor
        )
    ) {
        Text(
            text = "返回地图",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}
